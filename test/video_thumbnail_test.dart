import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:chat_app/utils/helper.dart';

void main() {
  // Initialize Flutter binding for tests
  TestWidgetsFlutterBinding.ensureInitialized();
  group('Video Thumbnail Tests', () {
    test('getVideoThumbnail should handle non-existent file gracefully',
        () async {
      // Create a non-existent file
      final nonExistentFile = File('/path/to/non/existent/video.mp4');

      // Test that it returns null without throwing
      final result = await Helper.getVideoThumbnail(nonExistentFile);

      expect(result, isNull);
    });

    test('getVideoThumbnail should handle invalid file gracefully', () async {
      // Create a temporary text file (not a video)
      final tempDir = Directory.systemTemp;
      final textFile = File('${tempDir.path}/test_text_file.txt');
      await textFile.writeAsString('This is not a video file');

      try {
        // Test that it returns null for non-video files
        final result = await Helper.getVideoThumbnail(textFile);

        expect(result, isNull);
      } finally {
        // Clean up
        if (await textFile.exists()) {
          await textFile.delete();
        }
      }
    });
  });
}
