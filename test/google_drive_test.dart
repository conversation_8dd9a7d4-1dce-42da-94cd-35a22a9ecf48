import 'package:flutter_test/flutter_test.dart';
import 'package:chat_app/utils/helper.dart';

void main() {
  group('Google Drive Video Link Tests', () {
    test('should detect Google Drive video links correctly', () {
      // Valid Google Drive video links
      const validLinks = [
        'https://drive.google.com/file/d/1KrfgqINosl5ubUgMb6133hqGH7pnaO8N/view?usp=drive_link',
        'https://drive.google.com/file/d/1KrfgqINosl5ubUgMb6133hqGH7pnaO8N/view',
        'https://drive.google.com/file/d/1KrfgqINosl5ubUgMb6133hqGH7pnaO8N/edit',
        'https://drive.google.com/file/d/abc123XYZ_-/view?usp=sharing',
      ];

      for (final link in validLinks) {
        expect(Helper.isGoogleDriveVideoLink(link), isTrue, reason: 'Failed for: $link');
      }
    });

    test('should reject non-Google Drive links', () {
      const invalidLinks = [
        'https://youtube.com/watch?v=abc123',
        'https://example.com/video.mp4',
        'https://drive.google.com/folder/abc123',
        'https://docs.google.com/document/d/abc123/edit',
        'not-a-url',
        '',
      ];

      for (final link in invalidLinks) {
        expect(Helper.isGoogleDriveVideoLink(link), isFalse, reason: 'Failed for: $link');
      }
    });

    test('should extract file ID correctly', () {
      const testCases = {
        'https://drive.google.com/file/d/1KrfgqINosl5ubUgMb6133hqGH7pnaO8N/view?usp=drive_link': 
            '1KrfgqINosl5ubUgMb6133hqGH7pnaO8N',
        'https://drive.google.com/file/d/abc123XYZ_-/view': 
            'abc123XYZ_-',
        'https://drive.google.com/file/d/test_file_id/edit?usp=sharing': 
            'test_file_id',
      };

      testCases.forEach((url, expectedId) {
        final actualId = Helper.getGoogleDriveFileId(url);
        expect(actualId, equals(expectedId), reason: 'Failed for: $url');
      });
    });

    test('should generate correct direct video URL', () {
      const testUrl = 'https://drive.google.com/file/d/1KrfgqINosl5ubUgMb6133hqGH7pnaO8N/view?usp=drive_link';
      const expectedDirectUrl = 'https://drive.google.com/uc?export=download&id=1KrfgqINosl5ubUgMb6133hqGH7pnaO8N';
      
      final actualDirectUrl = Helper.getGoogleDriveDirectVideoUrl(testUrl);
      expect(actualDirectUrl, equals(expectedDirectUrl));
    });

    test('should generate correct thumbnail URL', () {
      const testUrl = 'https://drive.google.com/file/d/1KrfgqINosl5ubUgMb6133hqGH7pnaO8N/view?usp=drive_link';
      const expectedThumbnailUrl = 'https://drive.google.com/thumbnail?id=1KrfgqINosl5ubUgMb6133hqGH7pnaO8N&sz=w400-h300';
      
      final actualThumbnailUrl = Helper.getGoogleDriveVideoThumbnail(testUrl);
      expect(actualThumbnailUrl, equals(expectedThumbnailUrl));
    });

    test('should return null for invalid URLs', () {
      const invalidUrls = [
        'https://youtube.com/watch?v=abc123',
        'not-a-url',
        '',
      ];

      for (final url in invalidUrls) {
        expect(Helper.getGoogleDriveFileId(url), isNull);
        expect(Helper.getGoogleDriveDirectVideoUrl(url), isNull);
        expect(Helper.getGoogleDriveVideoThumbnail(url), isNull);
      }
    });

    test('should detect Google Drive video in getTypeOfMessage', () {
      const googleDriveVideoUrl = 'https://drive.google.com/file/d/1KrfgqINosl5ubUgMb6133hqGH7pnaO8N/view?usp=drive_link';
      
      final messageType = Helper.getTypeOfMessage(googleDriveVideoUrl);
      expect(messageType, equals('video'));
    });
  });
}
