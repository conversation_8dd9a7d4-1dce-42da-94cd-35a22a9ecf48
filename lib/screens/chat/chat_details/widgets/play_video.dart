import 'dart:io';

import 'package:chat_app/utils/app_colors.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_player/video_player.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../../../export.dart';
import '../../../../utils/firestore_service.dart';

class PlayVideo extends StatefulWidget {
  final String videoUrl;
  final String? localPath;
  const PlayVideo({Key? key, required this.videoUrl, this.localPath})
      : super(key: key);

  @override
  State<PlayVideo> createState() => _PlayVideoState();
}

class _PlayVideoState extends State<PlayVideo> {
  VideoPlayerController? _videoPlayerController;
  bool _isLoading = true;
  bool _hasError = false;
  String? _localPath;

  @override
  void initState() {
    super.initState();
    _localPath = widget.localPath;
    _initializeVideo();
  }

  Future<void> _initializeVideo() async {
    try {
      if (kIsWeb) {
        // For web, always use network URL
        _videoPlayerController = VideoPlayerController.networkUrl(
          Uri.parse(widget.videoUrl),
        );
      } else {
        // For mobile, use local path if available, otherwise network
        if (_localPath != null && await File(_localPath!).exists()) {
          _videoPlayerController =
              VideoPlayerController.file(File(_localPath!));
        } else {
          _videoPlayerController = VideoPlayerController.networkUrl(
            Uri.parse(widget.videoUrl),
          );
        }
      }

      await _videoPlayerController!.initialize();
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
      });
    }
  }

  Future<void> _onDownload(String url) async {
    if (kIsWeb) {
      // For web, open video in new tab
      launchUrlString(url);
      return;
    }

    setState(() {
      _isLoading = true;
    });
    try {
      var ref = FirebaseStorage.instance.refFromURL(url);
      var bytes = await ref.getData();
      var dir = await getApplicationDocumentsDirectory();
      var file = File("${dir.path}/${ref.name}");
      await file.writeAsBytes(bytes!);
      setState(() {
        _localPath = file.path;
        _isLoading = false;
      });
      showToast("Downloaded");
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast("Download failed");
    }
  }

  @override
  void dispose() {
    _videoPlayerController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return OverlayLoading(
      isLoading: _isLoading,
      child: Scaffold(
        backgroundColor: AppColors.backgroundScaffold,
        appBar: CustomAppbar(
          title: "",
          actions: [
            IconButton(
              onPressed: () async {
                if (kIsWeb) {
                  launchUrlString(widget.videoUrl);
                } else if (_localPath != null) {
                  OpenFilex.open(_localPath ?? "");
                } else {
                  _videoPlayerController?.pause();
                  await _onDownload(widget.videoUrl);
                }
              },
              icon: kIsWeb
                  ? Icon(
                      Icons.open_in_new,
                      color: AppColors.white,
                    )
                  : _localPath != null
                      ? Icon(
                          Icons.folder,
                          color: AppColors.white,
                        )
                      : Icon(
                          Icons.download,
                          color: AppColors.white,
                        ),
            )
          ],
        ),
        body: SafeArea(
          child: Center(
            child: _buildVideoPlayer(),
          ),
        ),
      ),
    );
  }

  Widget _buildVideoPlayer() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              "Failed to load video",
              style: AppStyles.textSize16(color: AppColors.grey),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => launchUrlString(widget.videoUrl),
              child: const Text("Open in Browser"),
            ),
          ],
        ),
      );
    }

    if (_videoPlayerController == null ||
        !_videoPlayerController!.value.isInitialized) {
      return const Center(child: CircularProgressIndicator());
    }

    return Stack(
      alignment: Alignment.center,
      children: [
        AspectRatio(
          aspectRatio: _videoPlayerController!.value.aspectRatio,
          child: VideoPlayer(_videoPlayerController!),
        ),
        _VideoControls(controller: _videoPlayerController!),
      ],
    );
  }
}

class _VideoControls extends StatefulWidget {
  final VideoPlayerController controller;

  const _VideoControls({required this.controller});

  @override
  State<_VideoControls> createState() => _VideoControlsState();
}

class _VideoControlsState extends State<_VideoControls> {
  bool _showControls = true;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_updateState);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_updateState);
    super.dispose();
  }

  void _updateState() {
    if (mounted) setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _showControls = !_showControls;
        });
      },
      child: AnimatedOpacity(
        opacity: _showControls ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 300),
        child: Container(
          color: Colors.black26,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconButton(
                    onPressed: () {
                      if (widget.controller.value.isPlaying) {
                        widget.controller.pause();
                      } else {
                        widget.controller.play();
                      }
                    },
                    icon: Icon(
                      widget.controller.value.isPlaying
                          ? Icons.pause_circle_filled
                          : Icons.play_circle_filled,
                      color: Colors.white,
                      size: 64,
                    ),
                  ),
                ],
              ),
              VideoProgressIndicator(
                widget.controller,
                allowScrubbing: true,
                colors: VideoProgressColors(
                  playedColor: AppColors.primary,
                  bufferedColor: Colors.grey,
                  backgroundColor: Colors.grey.withOpacity(0.3),
                ),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }
}
